cd STEP3.SegmentationModel

healthy_datapath=/home/<USER>/DiffTumor/data/HealthyCT/ # (e.g., /data/HealthyCT/)
datapath=/home/<USER>/DiffTumor/data/Task03_Liver/ # (e.g., /data/10_Decathlon/Task03_Liver/)
cache_rate=1.0
batch_size=1
val_every=50
workers=8
organ=liver
fold=0

# U-Net
backbone=unet
logdir="runs/$organ.fold$fold.$backbone"
datafold_dir=cross_eval/"$organ"_aug_data_fold/
dist=$((RANDOM % 99999 + 10000))
CUDA_VISIBLE_DEVICES=7 python -W ignore main_generate_tumor.py --model_name $backbone --cache_rate $cache_rate --dist-url=tcp://127.0.0.1:$dist --workers $workers --max_epochs 2000 --val_every $val_every --batch_size=$batch_size --save_checkpoint --noamp --organ_type $organ --organ_model $organ --tumor_type tumor --fold $fold --ddim_ts 50 --logdir=$logdir --healthy_data_root $healthy_datapath --data_root $datapath --datafold_dir $datafold_dir

# CUDA_VISIBLE_DEVICES=7 python -W ignore main.py --model_name $backbone --cache_rate $cache_rate --dist-url=tcp://127.0.0.1:$dist --workers $workers --max_epochs 2000 --val_every $val_every --batch_size=$batch_size --save_checkpoint --distributed --noamp --organ_type $organ --organ_model $organ --tumor_type tumor --fold $fold --ddim_ts 50 --logdir=$logdir --healthy_data_root $healthy_datapath --data_root $datapath --datafold_dir $datafold_dir
